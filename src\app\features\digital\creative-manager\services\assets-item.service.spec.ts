import { TestBed } from '@angular/core/testing'

import { AssetsItemService } from './assets-item.service'
import { HttpClientTestingModule } from '@angular/common/http/testing'
import { LoginHelper } from '@app/shared/utils/login-helper'
import { MatLegacySnackBar } from '@angular/material/legacy-snack-bar'

describe('AssetsItemService', () => {
  let service: AssetsItemService
  class LoginHelperMock {
    isLogged() {
      return true
    }
  }

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],

      providers: [
        { provide: LoginHelper, useClass: LoginHelperMock },
        {
          provide: MatLegacySnackBar,
          useValue: jasmine.createSpyObj('MatLegacySnackBar', ['open']),
        },
      ],
    })
    service = TestBed.inject(AssetsItemService)
  })

  it('should be created', () => {
    expect(service).toBeTruthy()
  })
})
