import { ComponentFixture, TestBed } from '@angular/core/testing'

import { AssetsImagesListComponent } from './assets-images-list.component'
import { HttpClientTestingModule } from '@angular/common/http/testing'
import { MatDialogModule } from '@angular/material/dialog'
import { LoginHelper } from '@app/shared/utils/login-helper'

describe('AsstesImagesListComponent', () => {
  let component: AssetsImagesListComponent
  let fixture: ComponentFixture<AssetsImagesListComponent>
  class LoginHelperMock {
    isLogged() {
      return true
    }
  }

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [HttpClientTestingModule, MatDialogModule],
      declarations: [AssetsImagesListComponent],
      providers: [
        {
          LoginHelper,
          useValue: LoginHelperMock,
        },
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(AssetsImagesListComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
