import { ComponentFixture, TestBed } from '@angular/core/testing'

import { AsstesInfoComponent } from './asstes-info.component'
import { HttpClientTestingModule } from '@angular/common/http/testing'
import { MatLegacySnackBar } from '@angular/material/legacy-snack-bar'

describe('AsstesInfoComponent', () => {
  let component: AsstesInfoComponent
  let fixture: ComponentFixture<AsstesInfoComponent>

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [AsstesInfoComponent],
      imports: [HttpClientTestingModule],
      providers: [
        {
          provide: MatLegacySnackBar,
          useValue: jasmine.createSpyObj('MatLegacySnackBar', ['open']),
        },
        {
          provide: MatLegacySnackBar,
          useValue: jasmine.createSpyObj('MatLegacySnackBar', ['open']),
        },
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(AsstesInfoComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
