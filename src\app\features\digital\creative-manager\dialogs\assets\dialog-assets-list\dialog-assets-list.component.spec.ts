import { ComponentFixture, TestBed } from '@angular/core/testing'

import { DialogAssetsListComponent } from './dialog-assets-list.component'
import { NO_ERRORS_SCHEMA } from '@angular/core'
import {
  MatDialogRef,
  MAT_DIALOG_DATA,
  MatDialogModule,
} from '@angular/material/dialog'
import { HttpClientTestingModule } from '@angular/common/http/testing'
import { LoginHelper } from '@app/shared/utils/login-helper'

describe('DialogAssetsListComponent', () => {
  let component: DialogAssetsListComponent
  let fixture: ComponentFixture<DialogAssetsListComponent>

  class LoginHelperMock {
    isLogged() {
      return true
    }
  }

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [HttpClientTestingModule, MatDialogModule],
      declarations: [DialogAssetsListComponent],
      schemas: [NO_ERRORS_SCHEMA],
      providers: [
        {
          provide: LoginHelper,
          useValue: LoginHelperMock,
        },
        {
          provide: MatDialogRef,
          useValue: jasmine.createSpyObj('MatDialogRef', ['close']),
        },
        { provide: MAT_DIALOG_DATA, useValue: {} },
      ],
    }).compileComponents()

    fixture = TestBed.createComponent(DialogAssetsListComponent)
    component = fixture.componentInstance
    fixture.detectChanges()
  })

  it('should create', () => {
    expect(component).toBeTruthy()
  })
})
